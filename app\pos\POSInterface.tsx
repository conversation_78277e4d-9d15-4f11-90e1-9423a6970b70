
'use client';

import { useState } from 'react';

export default function POSInterface() {
  const [cart, setCart] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    phone: '',
    email: ''
  });
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showUnitModal, setShowUnitModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedUnit, setSelectedUnit] = useState('');
  const [unitQuantity, setUnitQuantity] = useState(1);

  const categories = ['All', 'Pain Relief', 'Vitamins', 'Diabetes', 'Antibiotics', 'Equipment', 'Supplements'];

  function buildImageUrl() {
    const baseUrl = 'https://readdy.ai/api/search-image';

    const params = new URLSearchParams({
      query: 'modern aspirin medication bottle white background pharmaceutical product clean minimalist',
      width: '200',
      height: '200',
      seq: 'aspirin1',
      orientation: 'squarish',
    });

    const fullUrl = `${baseUrl}?${params.toString()}`;
    return fullUrl;
  }

  const products = [
    {
      id: 1,
      name: 'Aspirin 500mg',
      price: 12.50,
      category: 'Pain Relief',
      image: buildImageUrl(),
      units: {
        tablet: { name: 'Tablet', stock: 1500, basePrice: 1.25 },
        strip: { name: 'Strip', stock: 150, basePrice: 12.50, contains: { tablet: 10 } },
        box: { name: 'Box', stock: 50, basePrice: 37.50, contains: { strip: 3, tablet: 30 } }
      }
    },
    {
      id: 2,
      name: 'Paracetamol 650mg',
      price: 8.75,
      category: 'Pain Relief',
      image: 'https://readdy.ai/api/search-image?query=paracetamol%20medication%20bottle%20white%20background%20pharmaceutical%20product%20clean%20minimalist&width=200&height=200&seq=paracetamol1&orientation=squarish',
      units: {
        tablet: { name: 'Tablet', stock: 2000, basePrice: 0.88 },
        strip: { name: 'Strip', stock: 200, basePrice: 8.75, contains: { tablet: 10 } },
        box: { name: 'Box', stock: 67, basePrice: 26.25, contains: { strip: 3, tablet: 30 } }
      }
    },
    {
      id: 3,
      name: 'Vitamin C 1000mg',
      price: 25.00,
      category: 'Vitamins',
      image: 'https://readdy.ai/api/search-image?query=vitamin%20c%20supplement%20bottle%20white%20background%20pharmaceutical%20product%20clean%20minimalist&width=200&height=200&seq=vitaminc1&orientation=squarish',
      units: {
        tablet: { name: 'Tablet', stock: 800, basePrice: 2.50 },
        strip: { name: 'Strip', stock: 80, basePrice: 25.00, contains: { tablet: 10 } },
        bottle: { name: 'Bottle', stock: 20, basePrice: 75.00, contains: { tablet: 30 } }
      }
    },
    {
      id: 4,
      name: 'Insulin Injection',
      price: 45.00,
      category: 'Diabetes',
      image: 'https://readdy.ai/api/search-image?query=insulin%20injection%20vial%20medical%20device%20white%20background%20pharmaceutical%20product%20clean%20minimalist&width=200&height=200&seq=insulin1&orientation=squarish',
      units: {
        vial: { name: 'Vial', stock: 100, basePrice: 45.00 },
        ampoule: { name: 'Ampoule', stock: 500, basePrice: 9.00 },
        box: { name: 'Box', stock: 20, basePrice: 225.00, contains: { vial: 5 } }
      }
    },
    {
      id: 5,
      name: 'Blood Pressure Monitor',
      price: 120.00,
      category: 'Equipment',
      image: 'https://readdy.ai/api/search-image?query=digital%20blood%20pressure%20monitor%20medical%20device%20white%20background%20clean%20minimalist&width=200&height=200&seq=bpmonitor1&orientation=squarish',
      units: {
        unit: { name: 'Unit', stock: 15, basePrice: 120.00 },
        box: { name: 'Box', stock: 5, basePrice: 115.00, contains: { unit: 1 } }
      }
    },
    {
      id: 6,
      name: 'Antibiotics Amoxicillin',
      price: 18.50,
      category: 'Antibiotics',
      image: 'https://readdy.ai/api/search-image?query=amoxicillin%20antibiotic%20medication%20bottle%20white%20background%20pharmaceutical%20product%20clean%20minimalist&width=200&height=200&seq=amoxicillin1&orientation=squarish',
      units: {
        capsule: { name: 'Capsule', stock: 600, basePrice: 1.85 },
        strip: { name: 'Strip', stock: 60, basePrice: 18.50, contains: { capsule: 10 } },
        bottle: { name: 'Bottle', stock: 15, basePrice: 55.50, contains: { capsule: 30 } }
      }
    },
    {
      id: 7,
      name: 'Omega-3 Fish Oil',
      price: 32.00,
      category: 'Supplements',
      image: 'https://readdy.ai/api/search-image?query=omega%203%20fish%20oil%20supplement%20bottle%20white%20background%20pharmaceutical%20product%20clean%20minimalist&width=200&height=200&seq=omega3&orientation=squarish',
      units: {
        capsule: { name: 'Capsule', stock: 450, basePrice: 3.20 },
        strip: { name: 'Strip', stock: 45, basePrice: 32.00, contains: { capsule: 10 } },
        bottle: { name: 'Bottle', stock: 12, basePrice: 96.00, contains: { capsule: 30 } }
      }
    },
    {
      id: 8,
      name: 'Multivitamin Complex',
      price: 28.75,
      category: 'Vitamins',
      image: 'https://readdy.ai/api/search-image?query=multivitamin%20supplement%20bottle%20white%20background%20pharmaceutical%20product%20clean%20minimalist&width=200&height=200&seq=multivitamin1&orientation=squarish',
      units: {
        tablet: { name: 'Tablet', stock: 900, basePrice: 2.88 },
        strip: { name: 'Strip', stock: 90, basePrice: 28.75, contains: { tablet: 10 } },
        bottle: { name: 'Bottle', stock: 25, basePrice: 86.25, contains: { tablet: 30 } }
      }
    },
  ];

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const openUnitModal = (product) => {
    setSelectedProduct(product);
    setSelectedUnit(Object.keys(product.units)[0]);
    setUnitQuantity(1);
    setShowUnitModal(true);
  };

  const addToCart = () => {
    if (!selectedProduct || !selectedUnit) return;

    const unitData = selectedProduct.units[selectedUnit];
    const cartItem = {
      id: `${selectedProduct.id}-${selectedUnit}`,
      productId: selectedProduct.id,
      name: selectedProduct.name,
      unit: selectedUnit,
      unitName: unitData.name,
      price: unitData.basePrice,
      quantity: unitQuantity,
      category: selectedProduct.category,
      image: selectedProduct.image,
      stock: unitData.stock
    };

    const existingItem = cart.find(item => item.id === cartItem.id);
    if (existingItem) {
      setCart(cart.map(item =>
        item.id === cartItem.id
          ? { ...item, quantity: item.quantity + unitQuantity }
          : item
      ));
    } else {
      setCart([...cart, cartItem]);
    }

    setShowUnitModal(false);
    setSelectedProduct(null);
    setSelectedUnit('');
    setUnitQuantity(1);
  };

  const updateQuantity = (id, quantity) => {
    if (quantity <= 0) {
      setCart(cart.filter(item => item.id !== id));
    } else {
      setCart(cart.map(item =>
        item.id === id ? { ...item, quantity } : item
      ));
    }
  };

  const removeFromCart = (id) => {
    setCart(cart.filter(item => item.id !== id));
  };

  const getSubtotal = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getTax = () => {
    return getSubtotal() * 0.08;
  };

  const getTotal = () => {
    return getSubtotal() + getTax();
  };

  const handleCheckout = () => {
    if (cart.length === 0) {
      return;
    }
    setShowPaymentModal(true);
  };

  const processPayment = () => {
    console.log('Processing payment:', { cart, customerInfo, total: getTotal(), paymentMethod });
    setCart([]);
    setCustomerInfo({ name: '', phone: '', email: '' });
    setShowPaymentModal(false);
    setPaymentMethod('cash');
  };

  const getUnitHierarchy = (product, unit) => {
    const unitData = product.units[unit];
    if (!unitData.contains) return '';

    const containsText = Object.entries(unitData.contains)
      .map(([containedUnit, count]) => `${count} ${product.units[containedUnit]?.name || containedUnit}${count > 1 ? 's' : ''}`)
      .join(', ');

    return `(${containsText})`;
  };

  return (
    <div className="flex-1 bg-gray-900 p-6">
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6 h-full">
        {/* Product Selection */}
        <div className="xl:col-span-3 bg-gray-800 rounded-lg border border-gray-700 flex flex-col">
          <div className="p-6 border-b border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white">Product Catalog</h2>
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                  <input
                    type="text"
                    placeholder="Search products..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm"
                  />
                </div>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors whitespace-nowrap">
                  <i className="ri-qr-scan-line mr-2"></i>
                  Scan Barcode
                </button>
              </div>
            </div>

            <div className="flex items-center space-x-2 overflow-x-auto">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors whitespace-nowrap ${selectedCategory === category
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          <div className="flex-1 p-6 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredProducts.map((product) => (
                <div key={product.id} className="bg-gray-700 border border-gray-600 rounded-lg p-4 hover:bg-gray-650 transition-colors">
                  <div className="aspect-square w-full bg-gray-600 rounded-lg mb-3 overflow-hidden">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover object-top"
                    />
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold text-white">{product.name}</h3>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-400">{product.category}</span>
                    </div>

                    {/* Unit Availability */}
                    <div className="space-y-1">
                      <p className="text-xs text-gray-400 font-medium">Available Units:</p>
                      <div className="flex flex-wrap gap-1">
                        {Object.entries(product.units).map(([unitKey, unitData]) => (
                          <span
                            key={unitKey}
                            className={`text-xs px-2 py-1 rounded-full ${unitData.stock > 0
                                ? 'bg-green-900 text-green-300'
                                : 'bg-red-900 text-red-300'
                              }`}
                          >
                            {unitData.name}: {unitData.stock}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-300">
                        <p>Starting from</p>
                        <span className="text-lg font-bold text-blue-400">
                          ${Math.min(...Object.values(product.units).map(u => u.basePrice)).toFixed(2)}
                        </span>
                      </div>
                      <button
                        onClick={() => openUnitModal(product)}
                        className="px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm whitespace-nowrap"
                      >
                        <i className="ri-add-line mr-1"></i>
                        Select Unit
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Cart & Checkout */}
        <div className="xl:col-span-1 bg-gray-800 rounded-lg border border-gray-700 flex flex-col">
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold text-white">Customer</h3>
              <button
                onClick={() => setShowCustomerModal(true)}
                className="px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm whitespace-nowrap"
              >
                <i className="ri-user-add-line mr-1"></i>
                Select
              </button>
            </div>
            {customerInfo.name ? (
              <div className="bg-gray-700 rounded-lg p-3">
                <p className="text-white font-medium">{customerInfo.name}</p>
                <p className="text-gray-400 text-sm">{customerInfo.phone}</p>
              </div>
            ) : (
              <div className="bg-gray-700 rounded-lg p-3 text-center">
                <p className="text-gray-400 text-sm">No customer selected</p>
              </div>
            )}
          </div>

          <div className="flex-1 p-4">
            <h3 className="text-lg font-semibold text-white mb-3">Cart ({cart.length})</h3>

            {cart.length === 0 ? (
              <div className="text-center py-8">
                <i className="ri-shopping-cart-line text-gray-600 text-4xl mb-2"></i>
                <p className="text-gray-400">Cart is empty</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {cart.map((item) => (
                  <div key={item.id} className="bg-gray-700 rounded-lg p-3">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-medium text-white text-sm">{item.name}</h4>
                        <p className="text-xs text-blue-400">{item.unitName}</p>
                      </div>
                      <button
                        onClick={() => removeFromCart(item.id)}
                        className="text-red-400 hover:text-red-300 transition-colors"
                      >
                        <i className="ri-close-line"></i>
                      </button>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-500 transition-colors"
                        >
                          <i className="ri-subtract-line text-gray-300 text-sm"></i>
                        </button>
                        <span className="w-8 text-center text-white font-medium">{item.quantity}</span>
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-500 transition-colors"
                        >
                          <i className="ri-add-line text-gray-300 text-sm"></i>
                        </button>
                      </div>
                      <span className="text-blue-400 font-medium">${(item.price * item.quantity).toFixed(2)}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {cart.length > 0 && (
            <div className="p-4 border-t border-gray-700">
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-gray-300">
                  <span>Subtotal:</span>
                  <span>${getSubtotal().toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-gray-300">
                  <span>Tax (8%):</span>
                  <span>${getTax().toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-xl font-bold text-white border-t border-gray-600 pt-2">
                  <span>Total:</span>
                  <span>${getTotal().toFixed(2)}</span>
                </div>
              </div>

              <div className="space-y-2">
                <button
                  onClick={() => setCart([])}
                  className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors whitespace-nowrap"
                >
                  <i className="ri-delete-bin-line mr-2"></i>
                  Clear Cart
                </button>
                <button
                  onClick={handleCheckout}
                  className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors whitespace-nowrap font-medium"
                >
                  <i className="ri-secure-payment-line mr-2"></i>
                  Proceed to Payment
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Unit Selection Modal */}
      {showUnitModal && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Select Unit Type</h3>
              <button
                onClick={() => setShowUnitModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <i className="ri-close-line text-xl"></i>
              </button>
            </div>

            <div className="mb-4">
              <h4 className="text-lg font-medium text-white mb-2">{selectedProduct.name}</h4>
              <p className="text-sm text-gray-400">{selectedProduct.category}</p>
            </div>

            <div className="space-y-3 mb-4">
              {Object.entries(selectedProduct.units).map(([unitKey, unitData]) => (
                <button
                  key={unitKey}
                  onClick={() => setSelectedUnit(unitKey)}
                  disabled={unitData.stock === 0}
                  className={`w-full p-3 rounded-lg border transition-colors text-left ${selectedUnit === unitKey
                      ? 'bg-blue-600 border-blue-500 text-white'
                      : unitData.stock === 0
                        ? 'bg-gray-700 border-gray-600 text-gray-500 cursor-not-allowed'
                        : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                    }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{unitData.name}</p>
                      <p className="text-sm opacity-75">
                        ${unitData.basePrice.toFixed(2)} {getUnitHierarchy(selectedProduct, unitKey)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className={`text-sm font-medium ${unitData.stock === 0 ? 'text-red-400' : 'text-green-400'
                        }`}>
                        {unitData.stock} available
                      </p>
                      {selectedUnit === unitKey && (
                        <i className="ri-check-line text-lg mt-1"></i>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Quantity
              </label>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setUnitQuantity(Math.max(1, unitQuantity - 1))}
                  className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-500 transition-colors"
                >
                  <i className="ri-subtract-line text-gray-300"></i>
                </button>
                <input
                  type="number"
                  min="1"
                  value={unitQuantity}
                  onChange={(e) => setUnitQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                  className="w-16 text-center px-2 py-1 bg-gray-700 border border-gray-600 rounded-lg text-white"
                />
                <button
                  onClick={() => setUnitQuantity(unitQuantity + 1)}
                  className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-500 transition-colors"
                >
                  <i className="ri-add-line text-gray-300"></i>
                </button>
              </div>
            </div>

            {selectedUnit && (
              <div className="bg-gray-700 rounded-lg p-3 mb-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Total Price:</span>
                  <span className="text-xl font-bold text-blue-400">
                    ${(selectedProduct.units[selectedUnit].basePrice * unitQuantity).toFixed(2)}
                  </span>
                </div>
              </div>
            )}

            <div className="flex space-x-3">
              <button
                onClick={() => setShowUnitModal(false)}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors whitespace-nowrap"
              >
                Cancel
              </button>
              <button
                onClick={addToCart}
                disabled={!selectedUnit || selectedProduct.units[selectedUnit]?.stock === 0}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors whitespace-nowrap"
              >
                <i className="ri-add-line mr-2"></i>
                Add to Cart
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Customer Modal */}
      {showCustomerModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Customer Information</h3>
              <button
                onClick={() => setShowCustomerModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <i className="ri-close-line text-xl"></i>
              </button>
            </div>
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Customer Name"
                value={customerInfo.name}
                onChange={(e) => setCustomerInfo({ ...customerInfo, name: e.target.value })}
                className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm"
              />
              <input
                type="tel"
                placeholder="Phone Number"
                value={customerInfo.phone}
                onChange={(e) => setCustomerInfo({ ...customerInfo, phone: e.target.value })}
                className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm"
              />
              <input
                type="email"
                placeholder="Email (Optional)"
                value={customerInfo.email}
                onChange={(e) => setCustomerInfo({ ...customerInfo, email: e.target.value })}
                className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm"
              />
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowCustomerModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors whitespace-nowrap"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setShowCustomerModal(false)}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors whitespace-nowrap"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Payment Method</h3>
              <button
                onClick={() => setShowPaymentModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <i className="ri-close-line text-xl"></i>
              </button>
            </div>

            <div className="mb-6">
              <div className="bg-gray-700 rounded-lg p-4">
                <div className="flex justify-between text-white mb-2">
                  <span>Total Amount:</span>
                  <span className="text-2xl font-bold text-blue-400">${getTotal().toFixed(2)}</span>
                </div>
              </div>
            </div>

            <div className="space-y-3 mb-6">
              <button
                onClick={() => setPaymentMethod('cash')}
                className={`w-full flex items-center justify-between p-3 rounded-lg border transition-colors ${paymentMethod === 'cash'
                    ? 'bg-blue-600 border-blue-500 text-white'
                    : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                  }`}
              >
                <div className="flex items-center space-x-3">
                  <i className="ri-money-dollar-circle-line text-xl"></i>
                  <span>Cash Payment</span>
                </div>
                {paymentMethod === 'cash' && <i className="ri-check-line text-xl"></i>}
              </button>

              <button
                onClick={() => setPaymentMethod('card')}
                className={`w-full flex items-center justify-between p-3 rounded-lg border transition-colors ${paymentMethod === 'card'
                    ? 'bg-blue-600 border-blue-500 text-white'
                    : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                  }`}
              >
                <div className="flex items-center space-x-3">
                  <i className="ri-bank-card-line text-xl"></i>
                  <span>Card Payment</span>
                </div>
                {paymentMethod === 'card' && <i className="ri-check-line text-xl"></i>}
              </button>

              <button
                onClick={() => setPaymentMethod('digital')}
                className={`w-full flex items-center justify-between p-3 rounded-lg border transition-colors ${paymentMethod === 'digital'
                    ? 'bg-blue-600 border-blue-500 text-white'
                    : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                  }`}
              >
                <div className="flex items-center space-x-3">
                  <i className="ri-smartphone-line text-xl"></i>
                  <span>Digital Wallet</span>
                </div>
                {paymentMethod === 'digital' && <i className="ri-check-line text-xl"></i>}
              </button>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowPaymentModal(false)}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors whitespace-nowrap"
              >
                Cancel
              </button>
              <button
                onClick={processPayment}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors whitespace-nowrap"
              >
                <i className="ri-check-line mr-2"></i>
                Complete Sale
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}