
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function WhatsAppCampaignPage() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [activeSidebar, setActiveSidebar] = useState('whatsapp');
  const [activeTab, setActiveTab] = useState('campaigns');
  const [campaigns, setCampaigns] = useState([
    {
      id: 1,
      name: 'Monthly Prescription Reminders',
      type: 'reminder',
      status: 'active',
      audience: 'prescription-due',
      audienceCount: 245,
      messagesSent: 245,
      delivered: 238,
      read: 195,
      replied: 42,
      scheduled: '2024-01-15 09:00',
      created: '2024-01-10',
      category: 'health',
      compliance: 'approved',
      template: 'prescription_reminder',
      budget: 150.00,
      spent: 123.75
    },
    {
      id: 2,
      name: 'Flu Season Awareness',
      type: 'promotional',
      status: 'scheduled',
      audience: 'all-customers',
      audienceCount: 1250,
      messagesSent: 0,
      delivered: 0,
      read: 0,
      replied: 0,
      scheduled: '2024-01-20 10:00',
      created: '2024-01-14',
      category: 'promotion',
      compliance: 'pending',
      template: 'health_promotion',
      budget: 300.00,
      spent: 0.00
    },
    {
      id: 3,
      name: 'Medication Adherence Follow-up',
      type: 'follow-up',
      status: 'completed',
      audience: 'recent-buyers',
      audienceCount: 180,
      messagesSent: 180,
      delivered: 178,
      read: 156,
      replied: 38,
      scheduled: '2024-01-12 14:00',
      created: '2024-01-08',
      category: 'health',
      compliance: 'approved',
      template: 'medication_followup',
      budget: 100.00,
      spent: 90.50
    }
  ]);

  const [audiences, setAudiences] = useState([
    {
      id: 1,
      name: 'Prescription Due Customers',
      description: 'Customers with prescriptions due for refill in next 7 days',
      count: 245,
      lastUpdated: '2024-01-15 08:30',
      criteria: {
        prescriptionDue: true,
        daysUntilDue: 7,
        optedIn: true
      }
    },
    {
      id: 2,
      name: 'Senior Citizens (65+)',
      description: 'Customers aged 65 and above with chronic conditions',
      count: 320,
      lastUpdated: '2024-01-15 08:15',
      criteria: {
        ageMin: 65,
        hasChronicConditions: true,
        optedIn: true
      }
    },
    {
      id: 3,
      name: 'Diabetes Patients',
      description: 'Customers with diabetes-related prescriptions',
      count: 180,
      lastUpdated: '2024-01-15 08:00',
      criteria: {
        conditionType: 'diabetes',
        activePrescriptions: true,
        optedIn: true
      }
    },
    {
      id: 4,
      name: 'VIP Customers',
      description: 'High-value customers with premium memberships',
      count: 85,
      lastUpdated: '2024-01-15 07:45',
      criteria: {
        customerTier: 'VIP',
        purchaseHistory: 'high',
        optedIn: true
      }
    }
  ]);

  const [templates, setTemplates] = useState([
    {
      id: 1,
      name: 'Prescription Reminder',
      category: 'health',
      type: 'reminder',
      subject: 'Prescription Refill Reminder',
      message: 'Hello {{name}}, your prescription for {{medication}} is due for refill. Please visit us or call {{phone}} to schedule pickup.',
      compliance: 'approved',
      usage: 245,
      lastUsed: '2024-01-15'
    },
    {
      id: 2,
      name: 'Health Promotion',
      category: 'promotion',
      type: 'promotional',
      subject: 'Important Health Information',
      message: 'Dear {{name}}, protect yourself this flu season! We have vaccines available. Book your appointment at {{phone}}.',
      compliance: 'approved',
      usage: 0,
      lastUsed: null
    },
    {
      id: 3,
      name: 'Medication Follow-up',
      category: 'health',
      type: 'follow-up',
      subject: 'Medication Check-in',
      message: 'Hi {{name}}, how are you feeling with your new medication {{medication}}? If you have any concerns, please contact us.',
      compliance: 'approved',
      usage: 180,
      lastUsed: '2024-01-12'
    }
  ]);

  const [analytics, setAnalytics] = useState({
    totalCampaigns: 12,
    activeCampaigns: 3,
    totalMessages: 2450,
    deliveryRate: 97.2,
    readRate: 78.5,
    responseRate: 15.8,
    optInRate: 85.3,
    monthlyBudget: 1000,
    monthlySpent: 680.25,
    complianceScore: 98.5
  });

  const [showCampaignModal, setShowCampaignModal] = useState(false);
  const [showAudienceModal, setShowAudienceModal] = useState(false);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [showComplianceModal, setShowComplianceModal] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState(null);
  const [selectedAudience, setSelectedAudience] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  const [newCampaign, setNewCampaign] = useState({
    name: '',
    type: 'reminder',
    audience: '',
    template: '',
    scheduledDate: '',
    scheduledTime: '',
    budget: '',
    category: 'health'
  });

  const [newAudience, setNewAudience] = useState({
    name: '',
    description: '',
    criteria: {
      ageMin: '',
      ageMax: '',
      conditionType: '',
      customerTier: '',
      prescriptionDue: false,
      optedIn: true
    }
  });

  const [newTemplate, setNewTemplate] = useState({
    name: '',
    category: 'health',
    type: 'reminder',
    subject: '',
    message: '',
    compliance: 'pending'
  });

  const sidebarItems = [
    { id: 'dashboard', icon: 'ri-dashboard-line', label: 'Dashboard', href: '/' },
    { id: 'inventory', icon: 'ri-medicine-bottle-line', label: 'Inventory', href: '/inventory' },
    { id: 'pos', icon: 'ri-cash-line', label: 'POS System', href: '/pos' },
    { id: 'prescriptions', icon: 'ri-file-list-3-line', label: 'Prescriptions', href: '/prescriptions' },
    { id: 'patients', icon: 'ri-user-heart-line', label: 'Patients', href: '/patients' },
    { id: 'customers', icon: 'ri-group-line', label: 'Customers', href: '/customers' },
    { id: 'analytics', icon: 'ri-bar-chart-line', label: 'Analytics', href: '/analytics' },
    { id: 'staff', icon: 'ri-team-line', label: 'Staff', href: '/staff' },
    { id: 'suppliers', icon: 'ri-truck-line', label: 'Suppliers', href: '/suppliers' },
    { id: 'orders', icon: 'ri-shopping-cart-line', label: 'Orders', href: '/orders' },
    { id: 'whatsapp', icon: 'ri-whatsapp-line', label: 'WhatsApp', href: '/whatsapp' },
    { id: 'expenses', icon: 'ri-money-dollar-circle-line', label: 'Expenses', href: '/expenses' }
  ];

  const campaignTypes = [
    { value: 'reminder', label: 'Prescription Reminder', icon: 'ri-alarm-line' },
    { value: 'promotional', label: 'Health Promotion', icon: 'ri-megaphone-line' },
    { value: 'follow-up', label: 'Follow-up Care', icon: 'ri-user-heart-line' },
    { value: 'educational', label: 'Health Education', icon: 'ri-book-line' },
    { value: 'emergency', label: 'Emergency Alert', icon: 'ri-error-warning-line' }
  ];

  const complianceRules = [
    { id: 1, rule: 'HIPAA Compliance', status: 'active', description: 'All messages must comply with HIPAA privacy regulations' },
    { id: 2, rule: 'Opt-in Requirement', status: 'active', description: 'Only send messages to customers who have opted in' },
    { id: 3, rule: 'Timing Restrictions', status: 'active', description: 'Messages only between 8 AM - 8 PM local time' },
    { id: 4, rule: 'Content Approval', status: 'active', description: 'All promotional content must be pre-approved' },
    { id: 5, rule: 'Frequency Limits', status: 'active', description: 'Maximum 1 promotional message per week per customer' }
  ];

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const handleCreateCampaign = () => {
    if (newCampaign.name && newCampaign.audience && newCampaign.template) {
      const selectedAud = audiences.find(a => a.id === parseInt(newCampaign.audience));
      const campaign = {
        id: campaigns.length + 1,
        ...newCampaign,
        audienceCount: selectedAud?.count || 0,
        messagesSent: 0,
        delivered: 0,
        read: 0,
        replied: 0,
        scheduled: `${newCampaign.scheduledDate} ${newCampaign.scheduledTime}`,
        created: new Date().toISOString().split('T')[0],
        status: 'scheduled',
        compliance: 'pending',
        budget: parseFloat(newCampaign.budget) || 0,
        spent: 0
      };
      setCampaigns([...campaigns, campaign]);
      setNewCampaign({
        name: '',
        type: 'reminder',
        audience: '',
        template: '',
        scheduledDate: '',
        scheduledTime: '',
        budget: '',
        category: 'health'
      });
      setShowCampaignModal(false);
    }
  };

  const handleCreateAudience = () => {
    if (newAudience.name && newAudience.description) {
      const audience = {
        id: audiences.length + 1,
        ...newAudience,
        count: Math.floor(Math.random() * 500) + 50,
        lastUpdated: new Date().toLocaleString()
      };
      setAudiences([...audiences, audience]);
      setNewAudience({
        name: '',
        description: '',
        criteria: {
          ageMin: '',
          ageMax: '',
          conditionType: '',
          customerTier: '',
          prescriptionDue: false,
          optedIn: true
        }
      });
      setShowAudienceModal(false);
    }
  };

  const handleCreateTemplate = () => {
    if (newTemplate.name && newTemplate.message) {
      const template = {
        id: templates.length + 1,
        ...newTemplate,
        usage: 0,
        lastUsed: null
      };
      setTemplates([...templates, template]);
      setNewTemplate({
        name: '',
        category: 'health',
        type: 'reminder',
        subject: '',
        message: '',
        compliance: 'pending'
      });
      setShowTemplateModal(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'scheduled': return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'completed': return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
      case 'paused': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  const getComplianceColor = (status) => {
    switch (status) {
      case 'approved': return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'pending': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      case 'rejected': return 'bg-red-500/20 text-red-300 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'health': return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'promotion': return 'bg-purple-500/20 text-purple-300 border-purple-500/30';
      case 'education': return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'emergency': return 'bg-red-500/20 text-red-300 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  const calculateDeliveryRate = (delivered, sent) => {
    if (sent === 0) return 0;
    return ((delivered / sent) * 100).toFixed(1);
  };

  const calculateReadRate = (read, delivered) => {
    if (delivered === 0) return 0;
    return ((read / delivered) * 100).toFixed(1);
  };

  const calculateResponseRate = (replied, read) => {
    if (read === 0) return 0;
    return ((replied / read) * 100).toFixed(1);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 text-gray-100 flex">
      {/* Sidebar */}
      <div className="w-64 bg-slate-800/50 backdrop-blur-xl border-r border-slate-700/50 flex flex-col">
        {/* Logo */}
        <div className="p-4 border-b border-slate-700/50">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
              <i className="ri-whatsapp-line text-white text-xl"></i>
            </div>
            <div>
              <h1 className="text-xl font-bold text-white" style={{ fontFamily: 'Pacifico, serif' }}>
                PharmaCare
              </h1>
              <p className="text-xs text-slate-400">WhatsApp Hub</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-3">
          <div className="space-y-2">
            {sidebarItems.map((item) => (
              <Link key={item.id} href={item.href}>
                <div className={`group flex items-center space-x-3 px-4 py-3 rounded-xl cursor-pointer transition-all duration-200 ${activeSidebar === item.id ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg' : 'text-slate-300 hover:bg-slate-700/50 hover:text-white'}`}>
                  <i className={`${item.icon} text-lg transition-transform group-hover:scale-110`}></i>
                  <span className="text-sm font-medium">{item.label}</span>
                  {activeSidebar === item.id && (
                    <div className="ml-auto w-2 h-2 bg-white rounded-full"></div>
                  )}
                </div>
              </Link>
            ))}
          </div>
        </nav>

        {/* User Profile */}
        <div className="p-4 border-t border-slate-700/50">
          <div className="flex items-center space-x-3 p-3 bg-slate-700/30 rounded-xl">
            <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
              <i className="ri-user-line text-white"></i>
            </div>
            <div>
              <p className="text-sm font-medium text-white">Campaign Manager</p>
              <p className="text-xs text-slate-400"><EMAIL></p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-slate-800/50 backdrop-blur-xl border-b border-slate-700/50 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div>
                <h2 className="text-2xl font-bold text-white">WhatsApp Campaign Hub</h2>
                <div className="flex items-center space-x-2 text-slate-400 mt-1">
                  <i className="ri-folder-line"></i>
                  <span className="text-sm">PharmaCare</span>
                  <i className="ri-arrow-right-s-line"></i>
                  <span className="text-sm">WhatsApp Marketing</span>
                </div>
              </div>
              
              {/* Real-time Stats */}
              <div className="hidden lg:flex items-center space-x-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{analytics.activeCampaigns}</div>
                  <div className="text-xs text-slate-400">Active</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{analytics.deliveryRate}%</div>
                  <div className="text-xs text-slate-400">Delivery</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{analytics.complianceScore}%</div>
                  <div className="text-xs text-slate-400">Compliance</div>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-xs text-slate-400">Current Time</p>
                <p className="text-sm font-mono text-white" suppressHydrationWarning={true}>
                  {currentTime.toLocaleTimeString()}
                </p>
              </div>
              
              <button
                onClick={() => setShowComplianceModal(true)}
                className="px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-600 text-white rounded-xl hover:from-yellow-600 hover:to-orange-700 transition-all duration-200 whitespace-nowrap shadow-lg"
              >
                <i className="ri-shield-check-line mr-2"></i>
                Compliance
              </button>
              
              <button
                onClick={() => setShowCampaignModal(true)}
                className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 whitespace-nowrap shadow-lg"
              >
                <i className="ri-add-line mr-2"></i>
                New Campaign
              </button>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 p-6 overflow-y-auto">
          {/* Analytics Dashboard */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-8">
            <div className="col-span-1 md:col-span-2 lg:col-span-2 xl:col-span-2 bg-gradient-to-r from-green-500/10 to-emerald-500/10 backdrop-blur-sm border border-green-500/20 rounded-2xl p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-green-300">Campaign Performance</h3>
                <div className="p-2 bg-green-500/20 rounded-lg">
                  <i className="ri-bar-chart-line text-green-400"></i>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-2xl font-bold text-green-400">{analytics.totalMessages}</p>
                  <p className="text-xs text-green-300/70">Messages Sent</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-400">{analytics.deliveryRate}%</p>
                  <p className="text-xs text-green-300/70">Delivery Rate</p>
                </div>
              </div>
            </div>
            
            <div className="col-span-1 md:col-span-2 lg:col-span-2 xl:col-span-2 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 backdrop-blur-sm border border-blue-500/20 rounded-2xl p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-blue-300">Engagement Metrics</h3>
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <i className="ri-eye-line text-blue-400"></i>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-2xl font-bold text-blue-400">{analytics.readRate}%</p>
                  <p className="text-xs text-blue-300/70">Read Rate</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-400">{analytics.responseRate}%</p>
                  <p className="text-xs text-blue-300/70">Response Rate</p>
                </div>
              </div>
            </div>
            
            <div className="col-span-1 md:col-span-2 lg:col-span-2 xl:col-span-2 bg-gradient-to-r from-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-purple-300">Budget Management</h3>
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <i className="ri-money-dollar-circle-line text-purple-400"></i>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-2xl font-bold text-purple-400">${analytics.monthlySpent}</p>
                  <p className="text-xs text-purple-300/70">Monthly Spent</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-purple-400">${analytics.monthlyBudget - analytics.monthlySpent}</p>
                  <p className="text-xs text-purple-300/70">Remaining</p>
                </div>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="bg-slate-800/50 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6 mb-6">
            <div className="flex space-x-1 bg-slate-700/30 rounded-xl p-1">
              <button
                onClick={() => setActiveTab('campaigns')}
                className={`px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium ${
                  activeTab === 'campaigns' 
                    ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg' 
                    : 'text-slate-300 hover:text-white hover:bg-slate-600/50'
                }`}
              >
                <i className="ri-megaphone-line mr-2"></i>
                Campaigns
              </button>
              <button
                onClick={() => setActiveTab('audiences')}
                className={`px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium ${
                  activeTab === 'audiences' 
                    ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg' 
                    : 'text-slate-300 hover:text-white hover:bg-slate-600/50'
                }`}
              >
                <i className="ri-group-line mr-2"></i>
                Audiences
              </button>
              <button
                onClick={() => setActiveTab('templates')}
                className={`px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium ${
                  activeTab === 'templates' 
                    ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg' 
                    : 'text-slate-300 hover:text-white hover:bg-slate-600/50'
                }`}
              >
                <i className="ri-message-3-line mr-2"></i>
                Templates
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className={`px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium ${
                  activeTab === 'analytics' 
                    ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg' 
                    : 'text-slate-300 hover:text-white hover:bg-slate-600/50'
                }`}
              >
                <i className="ri-line-chart-line mr-2"></i>
                Analytics
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="bg-slate-800/50 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6">
            {activeTab === 'campaigns' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-white">Campaign Management</h2>
                  <button
                    onClick={() => setShowCampaignModal(true)}
                    className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 whitespace-nowrap shadow-lg"
                  >
                    <i className="ri-add-line mr-2"></i>
                    Create Campaign
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                  {campaigns.map((campaign) => (
                    <div key={campaign.id} className="bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-xl p-6 hover:bg-slate-700/50 transition-all duration-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                            <i className={`${campaignTypes.find(t => t.value === campaign.type)?.icon || 'ri-message-line'} text-white`}></i>
                          </div>
                          <div>
                            <h3 className="font-semibold text-white">{campaign.name}</h3>
                            <p className="text-xs text-slate-400">{campaignTypes.find(t => t.value === campaign.type)?.label}</p>
                          </div>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(campaign.status)}`}>
                          {campaign.status}
                        </span>
                      </div>

                      <div className="space-y-3 mb-4">
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-400">Audience:</span>
                          <span className="text-white">{campaign.audienceCount} customers</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-400">Scheduled:</span>
                          <span className="text-white">{campaign.scheduled}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-400">Budget:</span>
                          <span className="text-white">${campaign.budget} / ${campaign.spent}</span>
                        </div>
                      </div>

                      {campaign.messagesSent > 0 && (
                        <div className="bg-slate-600/30 rounded-lg p-3 mb-4">
                          <div className="grid grid-cols-3 gap-2 text-center">
                            <div>
                              <div className="text-lg font-bold text-green-400">{calculateDeliveryRate(campaign.delivered, campaign.messagesSent)}%</div>
                              <div className="text-xs text-slate-400">Delivered</div>
                            </div>
                            <div>
                              <div className="text-lg font-bold text-blue-400">{calculateReadRate(campaign.read, campaign.delivered)}%</div>
                              <div className="text-xs text-slate-400">Read</div>
                            </div>
                            <div>
                              <div className="text-lg font-bold text-purple-400">{calculateResponseRate(campaign.replied, campaign.read)}%</div>
                              <div className="text-xs text-slate-400">Response</div>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded-full text-xs border ${getCategoryColor(campaign.category)}`}>
                            {campaign.category}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs border ${getComplianceColor(campaign.compliance)}`}>
                            {campaign.compliance}
                          </span>
                        </div>
                        <button
                          onClick={() => setSelectedCampaign(campaign)}
                          className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-lg hover:bg-blue-500/30 transition-colors text-sm"
                        >
                          View Details
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'audiences' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-white">Audience Segmentation</h2>
                  <button
                    onClick={() => setShowAudienceModal(true)}
                    className="px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-600 text-white rounded-xl hover:from-blue-600 hover:to-cyan-700 transition-all duration-200 whitespace-nowrap shadow-lg"
                  >
                    <i className="ri-add-line mr-2"></i>
                    Create Audience
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {audiences.map((audience) => (
                    <div key={audience.id} className="bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-xl p-6 hover:bg-slate-700/50 transition-all duration-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-full flex items-center justify-center">
                            <i className="ri-group-line text-white"></i>
                          </div>
                          <div>
                            <h3 className="font-semibold text-white">{audience.name}</h3>
                            <p className="text-xs text-slate-400">{audience.count} customers</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-blue-400">{audience.count}</div>
                          <div className="text-xs text-slate-400">Total</div>
                        </div>
                      </div>

                      <p className="text-sm text-slate-300 mb-4">{audience.description}</p>

                      <div className="bg-slate-600/30 rounded-lg p-3 mb-4">
                        <h4 className="text-sm font-medium text-slate-200 mb-2">Criteria:</h4>
                        <div className="space-y-1 text-xs text-slate-400">
                          {audience.criteria.ageMin && (
                            <div>Age: {audience.criteria.ageMin}+ years</div>
                          )}
                          {audience.criteria.conditionType && (
                            <div>Condition: {audience.criteria.conditionType}</div>
                          )}
                          {audience.criteria.customerTier && (
                            <div>Tier: {audience.criteria.customerTier}</div>
                          )}
                          {audience.criteria.prescriptionDue && (
                            <div>Prescription due in next 7 days</div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="text-xs text-slate-400">
                          Updated: {audience.lastUpdated}
                        </div>
                        <button
                          onClick={() => setSelectedAudience(audience)}
                          className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-lg hover:bg-blue-500/30 transition-colors text-sm"
                        >
                          Edit
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'templates' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-white">Message Templates</h2>
                  <button
                    onClick={() => setShowTemplateModal(true)}
                    className="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-xl hover:from-purple-600 hover:to-pink-700 transition-all duration-200 whitespace-nowrap shadow-lg"
                  >
                    <i className="ri-add-line mr-2"></i>
                    Create Template
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                  {templates.map((template) => (
                    <div key={template.id} className="bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-xl p-6 hover:bg-slate-700/50 transition-all duration-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
                            <i className="ri-message-3-line text-white"></i>
                          </div>
                          <div>
                            <h3 className="font-semibold text-white">{template.name}</h3>
                            <p className="text-xs text-slate-400">{template.type}</p>
                          </div>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs border ${getComplianceColor(template.compliance)}`}>
                          {template.compliance}
                        </span>
                      </div>

                      <div className="bg-slate-600/30 rounded-lg p-3 mb-4">
                        <h4 className="text-sm font-medium text-slate-200 mb-2">{template.subject}</h4>
                        <p className="text-xs text-slate-300 line-clamp-3">{template.message}</p>
                      </div>

                      <div className="flex items-center justify-between mb-4">
                        <span className={`px-2 py-1 rounded-full text-xs border ${getCategoryColor(template.category)}`}>
                          {template.category}
                        </span>
                        <div className="text-xs text-slate-400">
                          Used: {template.usage} times
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="text-xs text-slate-400">
                          Last used: {template.lastUsed || 'Never'}
                        </div>
                        <button
                          onClick={() => setSelectedTemplate(template)}
                          className="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-lg hover:bg-purple-500/30 transition-colors text-sm"
                        >
                          Edit
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'analytics' && (
              <div>
                <h2 className="text-xl font-bold text-white mb-6">Campaign Analytics</h2>
                
                {/* Real-time Analytics */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  <div className="bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-xl p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-medium text-slate-300">Total Messages</h3>
                      <div className="p-2 bg-blue-500/20 rounded-lg">
                        <i className="ri-message-line text-blue-400"></i>
                      </div>
                    </div>
                    <p className="text-3xl font-bold text-blue-400">{analytics.totalMessages}</p>
                    <p className="text-xs text-blue-300/70 mt-1">This month</p>
                  </div>
                  
                  <div className="bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-xl p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-medium text-slate-300">Delivery Rate</h3>
                      <div className="p-2 bg-green-500/20 rounded-lg">
                        <i className="ri-check-line text-green-400"></i>
                      </div>
                    </div>
                    <p className="text-3xl font-bold text-green-400">{analytics.deliveryRate}%</p>
                    <p className="text-xs text-green-300/70 mt-1">+2.3% from last month</p>
                  </div>
                  
                  <div className="bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-xl p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-medium text-slate-300">Read Rate</h3>
                      <div className="p-2 bg-purple-500/20 rounded-lg">
                        <i className="ri-eye-line text-purple-400"></i>
                      </div>
                    </div>
                    <p className="text-3xl font-bold text-purple-400">{analytics.readRate}%</p>
                    <p className="text-xs text-purple-300/70 mt-1">+1.8% from last month</p>
                  </div>
                  
                  <div className="bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-xl p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-medium text-slate-300">Response Rate</h3>
                      <div className="p-2 bg-yellow-500/20 rounded-lg">
                        <i className="ri-chat-1-line text-yellow-400"></i>
                      </div>
                    </div>
                    <p className="text-3xl font-bold text-yellow-400">{analytics.responseRate}%</p>
                    <p className="text-xs text-yellow-300/70 mt-1">+0.5% from last month</p>
                  </div>
                </div>

                {/* Campaign Performance Table */}
                <div className="bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-white mb-4">Campaign Performance</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-slate-600/50">
                          <th className="text-left py-3 px-4 font-semibold text-slate-300">Campaign</th>
                          <th className="text-left py-3 px-4 font-semibold text-slate-300">Sent</th>
                          <th className="text-left py-3 px-4 font-semibold text-slate-300">Delivered</th>
                          <th className="text-left py-3 px-4 font-semibold text-slate-300">Read</th>
                          <th className="text-left py-3 px-4 font-semibold text-slate-300">Replies</th>
                          <th className="text-left py-3 px-4 font-semibold text-slate-300">Budget</th>
                        </tr>
                      </thead>
                      <tbody>
                        {campaigns.map((campaign) => (
                          <tr key={campaign.id} className="border-b border-slate-600/30 hover:bg-slate-600/20">
                            <td className="py-3 px-4">
                              <div className="font-medium text-white">{campaign.name}</div>
                              <div className="text-sm text-slate-400">{campaign.type}</div>
                            </td>
                            <td className="py-3 px-4 text-blue-400">{campaign.messagesSent}</td>
                            <td className="py-3 px-4 text-green-400">{campaign.delivered}</td>
                            <td className="py-3 px-4 text-purple-400">{campaign.read}</td>
                            <td className="py-3 px-4 text-yellow-400">{campaign.replied}</td>
                            <td className="py-3 px-4 text-white">${campaign.spent}/${campaign.budget}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>

      {/* Create Campaign Modal */}
      {showCampaignModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-slate-800/90 backdrop-blur-xl border border-slate-700/50 rounded-2xl shadow-2xl p-8 w-full max-w-2xl">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                <i className="ri-megaphone-line text-white text-xl"></i>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Create New Campaign</h2>
                <p className="text-slate-400">Design your WhatsApp marketing campaign</p>
              </div>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Campaign Name</label>
                  <input
                    type="text"
                    placeholder="Enter campaign name"
                    value={newCampaign.name}
                    onChange={(e) => setNewCampaign({...newCampaign, name: e.target.value})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm text-white placeholder-slate-400 transition-all duration-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Campaign Type</label>
                  <select
                    value={newCampaign.type}
                    onChange={(e) => setNewCampaign({...newCampaign, type: e.target.value})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm text-white pr-8"
                  >
                    {campaignTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Target Audience</label>
                  <select
                    value={newCampaign.audience}
                    onChange={(e) => setNewCampaign({...newCampaign, audience: e.target.value})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm text-white pr-8"
                  >
                    <option value="">Select audience</option>
                    {audiences.map(audience => (
                      <option key={audience.id} value={audience.id}>{audience.name} ({audience.count})</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Message Template</label>
                  <select
                    value={newCampaign.template}
                    onChange={(e) => setNewCampaign({...newCampaign, template: e.target.value})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm text-white pr-8"
                  >
                    <option value="">Select template</option>
                    {templates.map(template => (
                      <option key={template.id} value={template.id}>{template.name}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Schedule Date</label>
                  <input
                    type="date"
                    value={newCampaign.scheduledDate}
                    onChange={(e) => setNewCampaign({...newCampaign, scheduledDate: e.target.value})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Schedule Time</label>
                  <input
                    type="time"
                    value={newCampaign.scheduledTime}
                    onChange={(e) => setNewCampaign({...newCampaign, scheduledTime: e.target.value})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Budget ($)</label>
                  <input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={newCampaign.budget}
                    onChange={(e) => setNewCampaign({...newCampaign, budget: e.target.value})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm text-white placeholder-slate-400"
                  />
                </div>
              </div>

              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <i className="ri-shield-check-line text-yellow-400"></i>
                  <span className="text-sm font-medium text-yellow-300">Compliance Check</span>
                </div>
                <p className="text-xs text-yellow-200">This campaign will be reviewed for HIPAA compliance and healthcare communication regulations before sending.</p>
              </div>
            </div>

            <div className="flex justify-end space-x-4 mt-8">
              <button
                onClick={() => setShowCampaignModal(false)}
                className="px-6 py-3 text-slate-300 border border-slate-600/50 rounded-xl hover:bg-slate-700/50 transition-all duration-200 whitespace-nowrap"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateCampaign}
                className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-200 whitespace-nowrap shadow-lg"
              >
                <i className="ri-add-line mr-2"></i>
                Create Campaign
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Audience Modal */}
      {showAudienceModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-slate-800/90 backdrop-blur-xl border border-slate-700/50 rounded-2xl shadow-2xl p-8 w-full max-w-2xl">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-full flex items-center justify-center">
                <i className="ri-group-line text-white text-xl"></i>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Create Audience Segment</h2>
                <p className="text-slate-400">Define your target audience criteria</p>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">Audience Name</label>
                <input
                  type="text"
                  placeholder="Enter audience name"
                  value={newAudience.name}
                  onChange={(e) => setNewAudience({...newAudience, name: e.target.value})}
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-white placeholder-slate-400 transition-all duration-200"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">Description</label>
                <textarea
                  placeholder="Describe your audience segment"
                  value={newAudience.description}
                  onChange={(e) => setNewAudience({...newAudience, description: e.target.value})}
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-white placeholder-slate-400 transition-all duration-200"
                  rows="3"
                  maxLength="500"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Minimum Age</label>
                  <input
                    type="number"
                    placeholder="e.g., 18"
                    value={newAudience.criteria.ageMin}
                    onChange={(e) => setNewAudience({...newAudience, criteria: {...newAudience.criteria, ageMin: e.target.value}})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-white placeholder-slate-400"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Maximum Age</label>
                  <input
                    type="number"
                    placeholder="e.g., 65"
                    value={newAudience.criteria.ageMax}
                    onChange={(e) => setNewAudience({...newAudience, criteria: {...newAudience.criteria, ageMax: e.target.value}})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-white placeholder-slate-400"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Condition Type</label>
                  <select
                    value={newAudience.criteria.conditionType}
                    onChange={(e) => setNewAudience({...newAudience, criteria: {...newAudience.criteria, conditionType: e.target.value}})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-white pr-8"
                  >
                    <option value="">Select condition</option>
                    <option value="diabetes">Diabetes</option>
                    <option value="hypertension">Hypertension</option>
                    <option value="asthma">Asthma</option>
                    <option value="heart-disease">Heart Disease</option>
                    <option value="arthritis">Arthritis</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Customer Tier</label>
                  <select
                    value={newAudience.criteria.customerTier}
                    onChange={(e) => setNewAudience({...newAudience, criteria: {...newAudience.criteria, customerTier: e.target.value}})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-white pr-8"
                  >
                    <option value="">Select tier</option>
                    <option value="VIP">VIP</option>
                    <option value="Premium">Premium</option>
                    <option value="Regular">Regular</option>
                    <option value="New">New</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={newAudience.criteria.prescriptionDue}
                    onChange={(e) => setNewAudience({...newAudience, criteria: {...newAudience.criteria, prescriptionDue: e.target.checked}})}
                    className="w-4 h-4 text-blue-500 bg-slate-700 border-slate-600 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-slate-300">Prescription due soon</span>
                </label>

                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={newAudience.criteria.optedIn}
                    onChange={(e) => setNewAudience({...newAudience, criteria: {...newAudience.criteria, optedIn: e.target.checked}})}
                    className="w-4 h-4 text-blue-500 bg-slate-700 border-slate-600 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-slate-300">WhatsApp opt-in required</span>
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-4 mt-8">
              <button
                onClick={() => setShowAudienceModal(false)}
                className="px-6 py-3 text-slate-300 border border-slate-600/50 rounded-xl hover:bg-slate-700/50 transition-all duration-200 whitespace-nowrap"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateAudience}
                className="px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-600 text-white rounded-xl hover:from-blue-600 hover:to-cyan-700 transition-all duration-200 whitespace-nowrap shadow-lg"
              >
                <i className="ri-add-line mr-2"></i>
                Create Audience
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Template Modal */}
      {showTemplateModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-slate-800/90 backdrop-blur-xl border border-slate-700/50 rounded-2xl shadow-2xl p-8 w-full max-w-2xl">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
                <i className="ri-message-3-line text-white text-xl"></i>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Create Message Template</h2>
                <p className="text-slate-400">Design your WhatsApp message template</p>
              </div>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Template Name</label>
                  <input
                    type="text"
                    placeholder="Enter template name"
                    value={newTemplate.name}
                    onChange={(e) => setNewTemplate({...newTemplate, name: e.target.value})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm text-white placeholder-slate-400 transition-all duration-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">Category</label>
                  <select
                    value={newTemplate.category}
                    onChange={(e) => setNewTemplate({...newTemplate, category: e.target.value})}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm text-white pr-8"
                  >
                    <option value="health">Health</option>
                    <option value="promotion">Promotion</option>
                    <option value="education">Education</option>
                    <option value="emergency">Emergency</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">Template Type</label>
                <select
                  value={newTemplate.type}
                  onChange={(e) => setNewTemplate({...newTemplate, type: e.target.value})}
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm text-white pr-8"
                >
                  <option value="reminder">Reminder</option>
                  <option value="promotional">Promotional</option>
                  <option value="follow-up">Follow-up</option>
                  <option value="educational">Educational</option>
                  <option value="emergency">Emergency</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">Subject</label>
                <input
                  type="text"
                  placeholder="Enter message subject"
                  value={newTemplate.subject}
                  onChange={(e) => setNewTemplate({...newTemplate, subject: e.target.value})}
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm text-white placeholder-slate-400 transition-all duration-200"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">Message Content</label>
                <textarea
                  placeholder="Enter message content. Use {{name}}, {{medication}}, {{phone}} for personalization."
                  value={newTemplate.message}
                  onChange={(e) => setNewTemplate({...newTemplate, message: e.target.value})}
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm text-white placeholder-slate-400 transition-all duration-200"
                  rows="4"
                  maxLength="500"
                />
                <div className="text-right text-xs text-slate-400 mt-1">
                  {newTemplate.message.length}/500 characters
                </div>
              </div>

              <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <i className="ri-information-line text-blue-400"></i>
                  <span className="text-sm font-medium text-blue-300">Template Variables</span>
                </div>
                <div className="text-xs text-blue-200 space-y-1">
                  <div><code>{'{name}'}</code> - Customer name</div>
                  <div><code>{'{medication}'}</code> - Medication name</div>
                  <div><code>{'{phone}'}</code> - Pharmacy phone number</div>
                  <div><code>{'{date}'}</code> - Current date</div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4 mt-8">
              <button
                onClick={() => setShowTemplateModal(false)}
                className="px-6 py-3 text-slate-300 border border-slate-600/50 rounded-xl hover:bg-slate-700/50 transition-all duration-200 whitespace-nowrap"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateTemplate}
                className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-xl hover:from-purple-600 hover:to-pink-700 transition-all duration-200 whitespace-nowrap shadow-lg"
              >
                <i className="ri-add-line mr-2"></i>
                Create Template
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Compliance Modal */}
      {showComplianceModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-slate-800/90 backdrop-blur-xl border border-slate-700/50 rounded-2xl shadow-2xl p-8 w-full max-w-4xl">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-full flex items-center justify-center">
                <i className="ri-shield-check-line text-white text-xl"></i>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Healthcare Compliance Center</h2>
                <p className="text-slate-400">HIPAA & healthcare communication regulations</p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Compliance Rules</h3>
                <div className="space-y-3">
                  {complianceRules.map((rule) => (
                    <div key={rule.id} className="bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-xl p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-white">{rule.rule}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs border ${rule.status === 'active' ? 'bg-green-500/20 text-green-300 border-green-500/30' : 'bg-red-500/20 text-red-300 border-red-500/30'}`}>
                          {rule.status}
                        </span>
                      </div>
                      <p className="text-sm text-slate-400">{rule.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Compliance Score</h3>
                <div className="bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-xl p-6 mb-6">
                  <div className="text-center">
                    <div className="text-4xl font-bold text-green-400 mb-2">{analytics.complianceScore}%</div>
                    <div className="text-sm text-slate-400">Overall Compliance Score</div>
                  </div>
                  <div className="mt-4 bg-slate-600/30 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${analytics.complianceScore}%` }}
                    ></div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-xl p-4">
                    <h4 className="font-medium text-white mb-2">HIPAA Compliance</h4>
                    <div className="flex items-center space-x-2">
                      <i className="ri-check-line text-green-400"></i>
                      <span className="text-sm text-slate-300">Patient data encryption enabled</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <i className="ri-check-line text-green-400"></i>
                      <span className="text-sm text-slate-300">Opt-in consent tracking active</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <i className="ri-check-line text-green-400"></i>
                      <span className="text-sm text-slate-300">Message audit trail maintained</span>
                    </div>
                  </div>

                  <div className="bg-slate-700/30 backdrop-blur-sm border border-slate-600/50 rounded-xl p-4">
                    <h4 className="font-medium text-white mb-2">Communication Guidelines</h4>
                    <div className="flex items-center space-x-2">
                      <i className="ri-check-line text-green-400"></i>
                      <span className="text-sm text-slate-300">Timing restrictions enforced</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <i className="ri-check-line text-green-400"></i>
                      <span className="text-sm text-slate-300">Content approval workflow active</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <i className="ri-check-line text-green-400"></i>
                      <span className="text-sm text-slate-300">Frequency limits monitored</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-8">
              <button
                onClick={() => setShowComplianceModal(false)}
                className="px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-600 text-white rounded-xl hover:from-yellow-600 hover:to-orange-700 transition-all duration-200 whitespace-nowrap shadow-lg"
              >
                <i className="ri-close-line mr-2"></i>
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
