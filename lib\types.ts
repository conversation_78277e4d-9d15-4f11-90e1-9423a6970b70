import { z } from 'zod';

// WhatsApp Message Schema
export const WhatsAppMessageSchema = z.object({
  id: z.number(),
  message: z.string(),
  timestamp: z.string(),
  type: z.enum(['sent', 'received']),
  status: z.enum(['delivered', 'read', 'pending']).optional(),
});

// Customer Schema
export const CustomerSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string().email(),
  phone: z.string(),
  address: z.string(),
  dateOfBirth: z.string(),
  totalPurchases: z.number(),
  lastVisit: z.string(),
  status: z.enum(['Active', 'Inactive', 'VIP']),
  prescriptions: z.number(),
  allergies: z.string(),
  insuranceProvider: z.string(),
  whatsappStatus: z.enum(['Online', 'Offline', 'Away']),
  lastWhatsappMessage: z.string(),
  whatsappOptIn: z.boolean(),
  messageCount: z.number(),
  avatar: z.string().url(),
  tags: z.array(z.string()),
  loyaltyPoints: z.number(),
  whatsappHistory: z.array(WhatsAppMessageSchema),
});

// New Customer Form Schema
export const NewCustomerSchema = z.object({
  name: z.string(),
  email: z.string(),
  phone: z.string(),
  address: z.string(),
  dateOfBirth: z.string(),
  allergies: z.string(),
  insuranceProvider: z.string(),
});

// Audience Criteria Schema
export const AudienceCriteriaSchema = z.object({
  prescriptionDue: z.boolean().optional(),
  daysUntilDue: z.number().optional(),
  optedIn: z.boolean(),
  ageMin: z.union([z.string(), z.number()]).optional(),
  ageMax: z.union([z.string(), z.number()]).optional(),
  hasChronicConditions: z.boolean().optional(),
  conditionType: z.string().optional(),
  activePrescriptions: z.boolean().optional(),
  customerTier: z.string().optional(),
  purchaseHistory: z.string().optional(),
});

// Audience Schema
export const AudienceSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string(),
  count: z.number(),
  lastUpdated: z.string(),
  criteria: AudienceCriteriaSchema,
});

// New Audience Form Schema
export const NewAudienceSchema = z.object({
  name: z.string(),
  description: z.string(),
  criteria: AudienceCriteriaSchema,
});

// Template Schema
export const TemplateSchema = z.object({
  id: z.number().optional(),
  name: z.string(),
  category: z.enum(['health', 'promotion', 'reminder', 'notification']),
  type: z.enum(['reminder', 'promotion', 'notification', 'alert']),
  subject: z.string(),
  message: z.string(),
  compliance: z.enum(['approved', 'pending', 'rejected']),
  lastUsed: z.string().optional(),
  usageCount: z.number().optional(),
});

// New Template Form Schema
export const NewTemplateSchema = z.object({
  name: z.string(),
  category: z.enum(['health', 'promotion', 'reminder', 'notification']),
  type: z.enum(['reminder', 'promotion', 'notification', 'alert']),
  subject: z.string(),
  message: z.string(),
  compliance: z.enum(['approved', 'pending', 'rejected']),
});

// Campaign Schema
export const CampaignSchema = z.object({
  id: z.number(),
  name: z.string(),
  status: z.enum(['active', 'scheduled', 'completed', 'paused']),
  audience: z.string(),
  template: z.string(),
  scheduledDate: z.string(),
  sentCount: z.number(),
  deliveredCount: z.number(),
  readCount: z.number(),
  responseCount: z.number(),
  category: z.enum(['health', 'promotion', 'reminder', 'notification']),
  compliance: z.enum(['approved', 'pending', 'rejected']),
});

// Export inferred TypeScript types
export type WhatsAppMessage = z.infer<typeof WhatsAppMessageSchema>;
export type Customer = z.infer<typeof CustomerSchema>;
export type NewCustomer = z.infer<typeof NewCustomerSchema>;
export type AudienceCriteria = z.infer<typeof AudienceCriteriaSchema>;
export type Audience = z.infer<typeof AudienceSchema>;
export type NewAudience = z.infer<typeof NewAudienceSchema>;
export type Template = z.infer<typeof TemplateSchema>;
export type NewTemplate = z.infer<typeof NewTemplateSchema>;
export type Campaign = z.infer<typeof CampaignSchema>;

// Common status and category types
export type CustomerStatus = 'Active' | 'Inactive' | 'VIP';
export type WhatsAppStatus = 'Online' | 'Offline' | 'Away';
export type MessageType = 'sent' | 'received';
export type MessageStatus = 'delivered' | 'read' | 'pending';
export type ComplianceStatus = 'approved' | 'pending' | 'rejected';
export type CampaignStatus = 'active' | 'scheduled' | 'completed' | 'paused';
export type CategoryType = 'health' | 'promotion' | 'reminder' | 'notification';
export type TemplateType = 'reminder' | 'promotion' | 'notification' | 'alert';

// Utility types for sorting and filtering
export type SortableCustomerFields = keyof Pick<Customer, 'name' | 'email' | 'totalPurchases' | 'lastVisit' | 'status'>;
export type FilterableStatuses = 'All' | CustomerStatus;
export type FilterableTags = 'All';
